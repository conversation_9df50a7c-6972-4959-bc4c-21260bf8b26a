"""
统一异常处理模块

定义应用中使用的各类自定义异常，以及异常处理机制
"""

class AppException(Exception):
    """应用异常基类"""
    def __init__(self, message="应用异常", status_code=500, details=None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

class AuthException(AppException):
    """认证和授权相关异常"""
    def __init__(self, message="认证异常", details=None):
        super().__init__(message=message, status_code=401, details=details)

class DataException(AppException):
    """数据处理相关异常"""
    def __init__(self, message="数据处理异常", details=None):
        super().__init__(message=message, status_code=400, details=details)

class BlockchainException(AppException):
    """区块链操作相关异常"""
    def __init__(self, message="区块链操作异常", details=None):
        super().__init__(message=message, status_code=500, details=details)

class ValidationException(AppException):
    """数据验证相关异常"""
    def __init__(self, message="数据验证失败", details=None):
        super().__init__(message=message, status_code=422, details=details)

class ResourceNotFoundException(AppException):
    """资源未找到异常"""
    def __init__(self, message="请求的资源不存在", details=None):
        super().__init__(message=message, status_code=404, details=details) 