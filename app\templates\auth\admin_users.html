{% extends "base.html" %}

{% block title %}用户管理 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .table-responsive {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
    }
    .user-actions {
        white-space: nowrap;
    }
    .user-status {
        width: 10px;
        height: 10px;
        display: inline-block;
        border-radius: 50%;
        margin-right: 5px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    .role-badge {
        font-size: 0.8em;
        padding: 0.35em 0.65em;
    }
    .role-admin {
        background-color: #dc3545;
    }
    .role-staff {
        background-color: #fd7e14;
    }
    .role-user {
        background-color: #6c757d;
    }
    .search-box {
        max-width: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h2><i class="fas fa-users me-2"></i>用户管理</h2>
            <p class="text-muted">管理系统中的所有用户账户</p>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group search-box">
                <input type="text" class="form-control" id="userSearch" placeholder="搜索用户...">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-6 text-md-end">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus me-1"></i>添加用户
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>角色</th>
                    <th>状态</th>
                    <th>注册时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                        {% if user.role == 'admin' %}
                        <span class="badge role-badge role-admin">管理员</span>
                        {% elif user.role == 'staff' %}
                        <span class="badge role-badge role-staff">员工</span>
                        {% else %}
                        <span class="badge role-badge role-user">普通用户</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if user.is_active %}
                        <span class="user-status status-active"></span>激活
                        {% else %}
                        <span class="user-status status-inactive"></span>未激活
                        {% endif %}
                    </td>
                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="user-actions">
                        <a href="{{ url_for('auth.admin_user_edit', user_id=user.id) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if user.id != current_user.id %}
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}">
                            <i class="fas fa-trash"></i>
                        </button>
                        
                        <!-- 删除确认模态框 -->
                        <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">确认删除</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>你确定要删除用户 <strong>{{ user.username }}</strong> 吗？此操作不可撤销。</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                        <form action="{{ url_for('auth.admin_user_delete', user_id=user.id) }}" method="POST" style="display: inline;">
                                            <button type="submit" class="btn btn-danger">确认删除</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if users|length == 0 %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>当前没有用户记录
    </div>
    {% endif %}
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('auth.admin_user_add') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色</label>
                        <select class="form-select" id="role" name="role">
                            <option value="user">普通用户</option>
                            <option value="staff">员工</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            账户激活
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">添加用户</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 用户搜索功能
        $("#userSearch").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("table tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>
{% endblock %} 