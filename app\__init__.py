from flask import Flask, jsonify, render_template, request
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_bcrypt import Bcrypt
from flask_login import LoginManager
from flask_cors import CORS
import os
import logging

from config import config

# 初始化扩展，但不绑定app
db = SQLAlchemy()
migrate = Migrate()
bcrypt = Bcrypt()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录才能访问此页面'
login_manager.login_message_category = 'info'

def create_app(config_name=None):
    """创建Flask应用工厂函数"""
    if not config_name:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    # 初始化应用
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    bcrypt.init_app(app)
    login_manager.init_app(app)
    CORS(app)
    
    # 注册自定义Jinja2过滤器
    app.jinja_env.filters['split'] = lambda value, delimiter: value.split(delimiter)
    
    # 注册蓝图
    from app.routes.auth import auth_bp
    from app.routes.main import main_bp
    from app.routes.survey import survey_bp
    from app.routes.api import api_bp
    
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(survey_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 配置日志
    if not app.debug:
        # 确保日志目录存在
        if not os.path.exists('logs'):
            os.mkdir('logs')
        # 设置文件处理器
        file_handler = logging.FileHandler('logs/app.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('应用启动')
    
    # 注册全局异常处理
    from app.utils.exceptions import AppException, AuthException, DataException, BlockchainException, ResourceNotFoundException
    
    @app.errorhandler(AppException)
    def handle_app_exception(error):
        app.logger.error(f"应用异常: {error.message}, 详情: {error.details}")
        if request.path.startswith('/api'):
            return jsonify({
                'success': False,
                'message': error.message,
                'details': error.details
            }), error.status_code
        return render_template('errors/error.html', error=error), error.status_code
    
    @app.errorhandler(404)
    def handle_not_found(error):
        if request.path.startswith('/api'):
            return jsonify({
                'success': False,
                'message': '请求的资源不存在'
            }), 404
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def handle_server_error(error):
        app.logger.error(f"服务器错误: {str(error)}")
        if request.path.startswith('/api'):
            return jsonify({
                'success': False,
                'message': '服务器内部错误'
            }), 500
        return render_template('errors/500.html'), 500
    
    # 创建数据库表（仅在需要时使用）
    with app.app_context():
        db.create_all()
        
        # 初始化区块链（确保在应用上下文中进行）
        from app.blockchain.chain import get_blockchain
        get_blockchain()
        
    return app 